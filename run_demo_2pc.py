#!/usr/bin/env python3
"""
2PC演示启动脚本
简化的启动脚本，用于运行Docker MySQL容器的2PC演示
"""

import sys
import subprocess

def check_requirements():
    """检查运行要求"""
    print("检查运行要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        return False
    
    # 检查必要的模块
    try:
        import mysql.connector
        import flask
        import colorlog
        print("✓ Python依赖已安装")
    except ImportError as e:
        print(f"错误: 缺少Python依赖 - {e}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    # 检查Docker
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✓ Docker可用")
        else:
            print("错误: Docker不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("错误: Docker未安装")
        return False
    
    return True

def main():
    """主函数"""
    print("=== 2PC分布式事务演示启动器 ===")
    print("使用Docker MySQL容器")
    print("=" * 50)
    
    # 检查运行要求
    if not check_requirements():
        print("\n请解决上述问题后重新运行")
        sys.exit(1)
    
    print("\n所有要求已满足，启动演示...")
    print("注意: 首次运行可能需要下载Docker镜像，请耐心等待")
    print("-" * 50)
    
    try:
        # 运行demo_2pc.py
        subprocess.run([sys.executable, 'demo_2pc.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"\n演示运行失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n运行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
