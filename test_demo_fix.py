#!/usr/bin/env python3
"""
测试demo_2pc修复的脚本
验证数据库查询结果格式是否正确
"""

import sys
import time
from database_manager import get_db_manager
from logger import log_system_info, log_system_error

def test_database_query():
    """测试数据库查询结果格式"""
    try:
        print("测试数据库查询结果格式...")
        
        # 测试使用execute_query方法
        print("1. 测试execute_query方法...")
        result = get_db_manager().execute_query('db1', 
            "SELECT product_id, product_name, quantity FROM inventory LIMIT 1")
        
        if result:
            print(f"   查询结果类型: {type(result)}")
            print(f"   第一行数据类型: {type(result[0])}")
            print(f"   第一行数据: {result[0]}")
            
            # 测试字典访问
            if isinstance(result[0], dict):
                print("   ✓ 结果是字典格式，可以使用键访问")
                print(f"   产品ID: {result[0]['product_id']}")
                print(f"   产品名称: {result[0]['product_name']}")
                print(f"   库存数量: {result[0]['quantity']}")
            else:
                print("   ✗ 结果不是字典格式")
                return False
        else:
            print("   ✗ 查询无结果")
            return False
        
        # 测试直接连接方式
        print("\n2. 测试直接连接方式...")
        conn = get_db_manager().get_connection('db1')
        
        # 测试普通cursor
        print("   测试普通cursor...")
        cursor = conn.cursor()
        cursor.execute("SELECT product_id, quantity FROM inventory LIMIT 1")
        result_tuple = cursor.fetchone()
        cursor.close()
        
        if result_tuple:
            print(f"   普通cursor结果类型: {type(result_tuple)}")
            print(f"   普通cursor结果: {result_tuple}")
        
        # 测试字典cursor
        print("   测试字典cursor...")
        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT product_id, quantity FROM inventory LIMIT 1")
        result_dict = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if result_dict:
            print(f"   字典cursor结果类型: {type(result_dict)}")
            print(f"   字典cursor结果: {result_dict}")
            print(f"   可以访问quantity: {result_dict['quantity']}")
        
        print("\n✓ 数据库查询测试通过")
        return True
        
    except Exception as e:
        print(f"\n✗ 数据库查询测试失败: {e}")
        return False

def test_demo_functions():
    """测试demo相关函数"""
    try:
        print("\n测试demo相关函数...")
        
        # 导入demo模块
        from demo_2pc import initialize_services
        
        # 测试服务初始化
        print("1. 测试服务初始化...")
        if initialize_services():
            print("   ✓ 服务初始化成功")
        else:
            print("   ✗ 服务初始化失败")
            return False
        
        # 测试账户余额查询
        print("2. 测试账户余额查询...")
        from demo_2pc import banking_service
        if banking_service:
            balance = banking_service.get_account_balance(1001)
            print(f"   账户1001余额: {balance}")
            if balance is not None:
                print("   ✓ 账户余额查询成功")
            else:
                print("   ✗ 账户余额查询失败")
                return False
        
        print("\n✓ demo函数测试通过")
        return True
        
    except Exception as e:
        print(f"\n✗ demo函数测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== Demo 2PC 修复验证测试 ===")
    print("=" * 40)
    
    success = True
    
    # 测试数据库查询
    if not test_database_query():
        success = False
    
    # 测试demo函数
    if not test_demo_functions():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✓ 所有测试通过，修复成功！")
        print("现在可以安全运行 demo_2pc.py")
    else:
        print("✗ 测试失败，需要进一步检查")
        sys.exit(1)

if __name__ == "__main__":
    main()
